<?php

/**
 * This is the model class for table "_sql_version".
 *
 * The followings are the available columns in table '_sql_version':
 * @property string $id
 * @property string $module
 * @property string $major_minor
 * @property int $revision
 * @property int $upgrade_block
 * @property string $updated_on
 */
class SqlVersion extends LoginActiveRecord
{
    public const MODULE = 'module';
    public const MAJOR_MINOR = 'major_minor';
    public const REVISION = 'revision';
    public const UPGRADE_BLOCK = 'upgrade_block';

    /**
     * @return string the associated database table name
     */
    public function tableName()
    {
        return '_sql_version';
    }

    /**
     * @return array validation rules for model attributes.
     */
    public function rules()
    {
        return [
            [
                'module, major_minor, revision, updated_on',
                'required',
                'message' => Dict::getValue('error_field_required')
            ],
            ['revision, upgrade_block', 'numerical', 'integerOnly' => true],
            ['module', 'length', 'max' => 128],
            ['major_minor', 'length', 'max' => 8],
            ['id, module, major_minor, revision, upgrade_block, updated_on', 'safe', 'on' => 'search'],
        ];
    }

    /**
     * @return array customized attribute labels (name=>label)
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'module' => 'Module',
            'major_minor' => 'Major Minor',
            'revision' => 'Revision',
            'upgrade_block' => 'Upgrade Block',
            'updated_on' => 'Updated On',
        ];
    }

    public function search()
    {
        $criteria = new CDbCriteria();

        $criteria->compare('id', $this->id, true);
        $criteria->compare('module', $this->module, true);
        $criteria->compare('major_minor', $this->major_minor, true);
        $criteria->compare('revision', $this->revision);
        $criteria->compare('upgrade_block', $this->upgrade_block);
        $criteria->compare('updated_on', $this->updated_on, true);

        return new CActiveDataProvider($this, [
            'criteria' => $criteria,
        ]);
    }

    public static function model($className = __CLASS__)
    {
        return parent::model($className);
    }

    public static function hasModule($module)
    {
        $SQL = 'SELECT module FROM _sql_version WHERE module = "' . $module . '"';
        $res = dbFetchRow($SQL);

        if (!empty($res)) {
            return true;
        }
        return false;
    }

    public static function getAllModules()
    {
        $modules = self::model()->findAll(['select' => 'module']);

        if (!empty($modules)) {
            return array_map(function ($module) {
                return $module->module;
            }, $modules);
        } else {
            return [];
        }
    }

    public function blockingModule(string $module): void
    {
        self::model()->updateAll(
            [self::UPGRADE_BLOCK => 1],
            self::MODULE ."= '{$module}'"
        );
    }
}
